/**
 * DL Engine 类型声明文件
 * 为 dl-engine.mjs 提供 TypeScript 类型支持
 */

// 基础类型和枚举
export enum DependencyType {
  STRONG = 'strong',
  WEAK = 'weak',
  LAZY = 'lazy'
}

export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  MATERIAL = 'material',
  SHADER = 'shader',
  AUDIO = 'audio',
  SCENE = 'scene',
  ANIMATION = 'animation',
  SCRIPT = 'script',
  FONT = 'font',
  VIDEO = 'video',
  DATA = 'data'
}

export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  PHONG = 'phong',
  LAMBERT = 'lambert'
}

export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AMBIENT = 'ambient'
}

export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic'
}

export enum AnimationBlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive'
}

export enum AnimationLoopMode {
  ONCE = 'once',
  REPEAT = 'repeat',
  PING_PONG = 'pingpong'
}

export enum AnimationEventType {
  START = 'start',
  END = 'end',
  LOOP = 'loop'
}

export enum AudioEventType {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  END = 'end'
}

export enum AudioSourceState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused'
}

export enum AudioSourceEventType {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  VOLUME_CHANGE = 'volumeChange'
}

export enum AudioType {
  MUSIC = 'music',
  SFX = 'sfx',
  VOICE = 'voice',
  AMBIENT = 'ambient'
}

export enum NetworkEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  ERROR = 'error'
}

export enum NetworkState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting'
}

export enum NetworkConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed'
}

export enum WebRTCConnectionState {
  NEW = 'new',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  FAILED = 'failed',
  CLOSED = 'closed'
}

export enum WebSocketConnectionState {
  CONNECTING = 'connecting',
  OPEN = 'open',
  CLOSING = 'closing',
  CLOSED = 'closed'
}

export enum NetworkEntityType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic'
}

export enum NetworkEntitySyncMode {
  NONE = 'none',
  TRANSFORM = 'transform',
  FULL = 'full'
}

export enum NetworkEntityOwnershipMode {
  SERVER = 'server',
  CLIENT = 'client',
  SHARED = 'shared'
}

export enum NetworkUserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}

export enum NetworkUserState {
  OFFLINE = 'offline',
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy'
}

export enum MessageType {
  TEXT = 'text',
  BINARY = 'binary',
  JSON = 'json'
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export enum UILayoutType {
  NONE = 'none',
  GRID = 'grid',
  FLEX = 'flex',
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative'
}

export enum UIEventType {
  CLICK = 'click',
  HOVER = 'hover',
  DRAG_START = 'dragstart',
  DRAG = 'drag',
  DRAG_END = 'dragend',
  FOCUS = 'focus',
  BLUR = 'blur',
  KEY_DOWN = 'keydown',
  KEY_UP = 'keyup'
}

export enum UIAnimationType {
  FADE = 'fade',
  SCALE = 'scale',
  MOVE = 'move',
  ROTATE = 'rotate',
  COLOR = 'color'
}

export enum UIComponentType {
  BASE = 'base',
  CONTAINER = 'container',
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  SLIDER = 'slider',
  CHECKBOX = 'checkbox',
  DROPDOWN = 'dropdown',
  PANEL = 'panel',
  WINDOW = 'window',
  CUSTOM = 'custom'
}

// 依赖信息接口
export interface DependencyInfo {
  id: string;
  type: DependencyType;
  priority?: number;
}

// 输入相关枚举
export enum InputEventType {
  KEY_DOWN = 'keydown',
  KEY_UP = 'keyup',
  MOUSE_DOWN = 'mousedown',
  MOUSE_UP = 'mouseup',
  MOUSE_MOVE = 'mousemove',
  TOUCH_START = 'touchstart',
  TOUCH_END = 'touchend',
  TOUCH_MOVE = 'touchmove'
}

export enum InputActionType {
  BUTTON = 'button',
  AXIS = 'axis',
  VECTOR = 'vector'
}

export enum InputMappingType {
  BUTTON = 'button',
  AXIS = 'axis',
  COMPOSITE = 'composite',
  VECTOR = 'vector'
}

export enum KeyState {
  UP = 'up',
  DOWN = 'down',
  PRESSED = 'pressed',
  RELEASED = 'released'
}

export enum MouseButton {
  LEFT = 0,
  MIDDLE = 1,
  RIGHT = 2
}

export enum MouseButtonState {
  UP = 'up',
  DOWN = 'down',
  PRESSED = 'pressed',
  RELEASED = 'released'
}

// 物理相关枚举
export enum SoftBodyType {
  CLOTH = 'cloth',
  ROPE = 'rope',
  VOLUME = 'volume'
}

// 交互相关枚举
export enum InteractionType {
  HOVER = 'hover',
  CLICK = 'click',
  GRAB = 'grab',
  TOUCH = 'touch'
}

export enum InteractionEventType {
  START = 'start',
  UPDATE = 'update',
  END = 'end'
}

export enum GrabEventType {
  GRAB_START = 'grabStart',
  GRAB_UPDATE = 'grabUpdate',
  GRAB_END = 'grabEnd'
}

export enum GrabType {
  HAND = 'hand',
  CONTROLLER = 'controller',
  MOUSE = 'mouse'
}

export enum GrabState {
  IDLE = 'idle',
  HOVERING = 'hovering',
  GRABBING = 'grabbing'
}

export enum HighlightType {
  OUTLINE = 'outline',
  GLOW = 'glow',
  COLOR = 'color'
}

export enum PromptPositionType {
  WORLD = 'world',
  SCREEN = 'screen',
  ATTACHED = 'attached'
}

// 角色控制相关枚举
export enum CharacterMovementMode {
  WALKING = 'walking',
  RUNNING = 'running',
  FLYING = 'flying',
  SWIMMING = 'swimming'
}

export enum CharacterState {
  IDLE = 'idle',
  WALKING = 'walking',
  RUNNING = 'running',
  JUMPING = 'jumping',
  FALLING = 'falling',
  LANDING = 'landing'
}

export enum ControllerPresetType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  CUSTOM = 'custom'
}

// 压缩相关枚举
export enum CompressionLevel {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  MAXIMUM = 'maximum'
}

// 粒子系统相关枚举
export enum EmitterShapeType {
  POINT = 'point',
  BOX = 'box',
  SPHERE = 'sphere',
  CONE = 'cone',
  CIRCLE = 'circle'
}

// 场景相关枚举
export enum SceneTransitionType {
  FADE = 'fade',
  SLIDE = 'slide',
  DISSOLVE = 'dissolve',
  INSTANT = 'instant'
}

export enum SkyboxType {
  COLOR = 'color',
  GRADIENT = 'gradient',
  CUBEMAP = 'cubemap',
  HDRI = 'hdri'
}

// 资源相关枚举
export enum ResourceState {
  UNLOADED = 'unloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

// 碰撞检测相关枚举
export enum ContinuousCollisionDetection {
  DISABLED = 'disabled',
  ENABLED = 'enabled',
  ENHANCED = 'enhanced'
}

// 约束相关枚举
export enum ConstraintType {
  FIXED = 'fixed',
  HINGE = 'hinge',
  SLIDER = 'slider',
  WHEEL = 'wheel',
  SPRING = 'spring'
}

// 混合相关枚举
export enum CompositeBindingType {
  AND = 'and',
  OR = 'or',
  XOR = 'xor'
}

// 事件发射器基类
export declare class EventEmitter {
  on(event: string, callback: (...args: any[]) => void): this;
  once(event: string, callback: (...args: any[]) => void): this;
  off(event: string, callback?: (...args: any[]) => void): this;
  emit(event: string, ...args: any[]): boolean;
  listenerCount(event?: string): number;
  eventNames(): string[];
  listeners(event: string): ((...args: any[]) => void)[];
  removeAllListeners(): this;
}

// 系统基类
export declare class System extends EventEmitter {
  readonly priority: number;
  enabled: boolean;

  constructor(priority?: number);
  initialize(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
}

// 时间类
export declare class Time {
  static deltaTime: number;
  static fixedDeltaTime: number;
  static time: number;
  static timeScale: number;
  static frameCount: number;

  static getDeltaTime(): number;
  static getFixedDeltaTime(): number;
  static getTime(): number;
  static getTimeScale(): number;
  static setTimeScale(scale: number): void;
  static getFrameCount(): number;
}

// 调试类
export declare class Debug {
  static enabled: boolean;

  static log(category: string, message: string, ...args: any[]): void;
  static warn(category: string, message: string, ...args: any[]): void;
  static error(category: string, message: string, ...args: any[]): void;
  static setEnabled(enabled: boolean): void;
  static isEnabled(): boolean;
}

// 世界类
export declare class World extends EventEmitter {
  readonly systems: Map<string, System>;

  constructor();
  addSystem<T extends System>(system: T): T;
  removeSystem<T extends System>(systemType: string): T | null;
  getSystem<T extends System>(systemType: string): T | null;
  hasSystem(systemType: string): boolean;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 渲染器类
export declare class Renderer extends EventEmitter {
  readonly canvas: HTMLCanvasElement;
  readonly renderer: THREE.WebGLRenderer;

  constructor(canvas: HTMLCanvasElement, options?: any);
  render(scene: THREE.Scene, camera: THREE.Camera): void;
  setSize(width: number, height: number): void;
  getSize(): { width: number; height: number };
  dispose(): void;
}

// 引擎主类
export declare class Engine extends EventEmitter {
  static getInstance(): Engine;

  readonly world: World;
  readonly renderer: Renderer;
  readonly sceneManager: SceneManager;
  readonly resourceManager: ResourceManager;

  initialize(canvas: HTMLCanvasElement, options?: any): Promise<void>;
  start(): void;
  stop(): void;
  pause(): void;
  resume(): void;
  update(deltaTime: number): void;
  render(): void;
  dispose(): void;

  getWorld(): World;
  getRenderer(): Renderer;
  getSceneManager(): SceneManager;
  getResourceManager(): ResourceManager;
  getDependencyManager(): DependencyManager;
}

// 场景管理器
export declare class SceneManager extends EventEmitter {
  static getInstance(): SceneManager;

  createScene(name: string): Scene;
  getScene(name: string): Scene | null;
  setActiveScene(name: string): boolean;
  getActiveScene(): Scene | null;
  removeScene(name: string): boolean;
  getAllScenes(): Scene[];
  loadScene(url: string): Promise<Scene>;
  saveScene(scene: Scene, url: string): Promise<void>;
}

// 场景类
export declare class Scene extends EventEmitter {
  readonly name: string;
  readonly entities: Map<string, Entity>;
  readonly threeScene: THREE.Scene;

  constructor(name: string);
  addEntity(entity: Entity): void;
  removeEntity(entity: Entity): void;
  getEntity(id: string): Entity | null;
  getEntities(): Entity[];
  getThreeScene(): THREE.Scene;
  update(deltaTime: number): void;
  dispose(): void;
}

// 资源管理器
export declare class ResourceManager extends EventEmitter {
  static getInstance(): ResourceManager;

  loadResource<T = any>(url: string, type?: AssetType): Promise<T>;
  getResource<T = any>(url: string): T | null;
  unloadResource(url: string): boolean;
  preloadResources(urls: string[]): Promise<void>;
  getLoadedResources(): Map<string, any>;
  clearCache(): void;
  setBasePath(path: string): void;
  getBasePath(): string;
}

// 依赖管理器
export declare class DependencyManager extends EventEmitter {
  static getInstance(): DependencyManager;

  addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number): void;
  removeDependency(resourceId: string, dependencyId: string): void;
  getDependencies(resourceId: string): DependencyInfo[];
  getDependents(resourceId: string): string[];
  resolveDependencies(resourceId: string): string[];
  checkCircularDependencies(): string[][];
}

// 组件基类
export declare class Component extends EventEmitter {
  readonly type: string;
  entity: Entity | null;
  enabled: boolean;

  constructor(type: string);
  getType(): string;
  setEntity(entity: Entity): void;
  getEntity(): Entity | null;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  onAttach(): void;
  onEnable(): void;
  onDisable(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 实体类
export declare class Entity extends EventEmitter {
  readonly id: string;
  name: string;
  active: boolean;

  constructor(name?: string);
  addComponent<T extends Component>(component: T): T;
  removeComponent<T extends Component>(componentType: string): T | null;
  getComponent<T extends Component>(componentType: string): T | null;
  getComponents<T extends Component>(componentType: string): T[];
  hasComponent(componentType: string): boolean;
  setActive(active: boolean): void;
  isActive(): boolean;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  dispose(): void;
}

// 变换组件
export declare class Transform extends Component {
  static readonly type: string;

  setPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getPosition(): THREE.Vector3;
  setWorldPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
  getWorldPosition(): THREE.Vector3;
  setRotation(x: number | THREE.Euler, y?: number, z?: number, order?: string): void;
  getRotation(): THREE.Euler;
  setScale(x: number | THREE.Vector3, y?: number, z?: number): void;
  getScale(): THREE.Vector3;
  lookAt(target: THREE.Vector3, up?: THREE.Vector3): void;
  setParent(parent: Transform | null): void;
  getParent(): Transform | null;
  getChildren(): Transform[];
  getObject3D(): THREE.Object3D;
}

// 相机组件
export declare class Camera extends Component {
  static readonly type: string;

  readonly camera: THREE.Camera;
  cameraType: CameraType;

  constructor(type?: CameraType);
  getCamera(): THREE.Camera;
  setCameraType(type: CameraType): void;
  getCameraType(): CameraType;
  setFOV(fov: number): void;
  getFOV(): number;
  setNear(near: number): void;
  getNear(): number;
  setFar(far: number): void;
  getFar(): number;
  setAspect(aspect: number): void;
  getAspect(): number;
}

// 光源组件
export declare class Light extends Component {
  static readonly type: string;

  readonly light: THREE.Light;
  lightType: LightType;

  constructor(type?: LightType);
  getLight(): THREE.Light;
  setLightType(type: LightType): void;
  getLightType(): LightType;
  setColor(color: THREE.Color | string | number): void;
  getColor(): THREE.Color;
  setIntensity(intensity: number): void;
  getIntensity(): number;
}

// 动画器组件
export declare class Animator extends Component {
  static readonly type: string;

  readonly mixer: THREE.AnimationMixer;
  readonly clips: Map<string, THREE.AnimationClip>;
  readonly actions: Map<string, THREE.AnimationAction>;

  constructor();
  addClip(name: string, clip: THREE.AnimationClip): void;
  removeClip(name: string): boolean;
  getClip(name: string): THREE.AnimationClip | null;
  play(clipName: string, fadeTime?: number): THREE.AnimationAction | null;
  stop(clipName: string, fadeTime?: number): void;
  pause(clipName: string): void;
  resume(clipName: string): void;
  setWeight(clipName: string, weight: number): void;
  getWeight(clipName: string): number;
  setLoop(clipName: string, loop: AnimationLoopMode): void;
  getLoop(clipName: string): AnimationLoopMode;
  setSpeed(clipName: string, speed: number): void;
  getSpeed(clipName: string): number;
  getTime(): number;
  update(deltaTime: number): void;
}

// 音频监听器组件
export declare class AudioListener extends Component {
  static readonly type: string;

  readonly listener: THREE.AudioListener;

  constructor();
  getListener(): THREE.AudioListener;
  setMasterVolume(volume: number): void;
  getMasterVolume(): number;
}

// 音频源组件
export declare class AudioSource extends Component {
  static readonly type: string;

  readonly audio: THREE.Audio | THREE.PositionalAudio;
  audioType: AudioType;
  state: AudioSourceState;

  constructor(type?: AudioType);
  loadAudio(url: string): Promise<void>;
  play(): void;
  pause(): void;
  stop(): void;
  setVolume(volume: number): void;
  getVolume(): number;
  setLoop(loop: boolean): void;
  getLoop(): boolean;
  setPlaybackRate(rate: number): void;
  getPlaybackRate(): number;
  getDuration(): number;
  getCurrentTime(): number;
  setCurrentTime(time: number): void;
  getState(): AudioSourceState;
}



// 动画状态机相关类型
export interface AnimationStateMachineData {
  states: AnimationStateData[];
  transitions: TransitionData[];
  parameters: ParameterData[];
  defaultState?: string;
}

export interface AnimationStateData {
  name: string;
  type: 'SingleAnimationState' | 'BlendAnimationState';
  clipName?: string;
  loop?: boolean;
  parameterName?: string;
  blendSpaceType?: '1D' | '2D';
}

export interface TransitionData {
  from: string;
  to: string;
  duration: number;
  condition: () => boolean;
}

export interface ParameterData {
  name: string;
  type: 'float' | 'int' | 'bool' | 'trigger';
  defaultValue: any;
  metadata?: any;
}

// 动画状态机类
export declare class AnimationStateMachine {
  constructor(animator: Animator);
  addState(state: AnimationStateData): void;
  addTransition(rule: TransitionData): void;
  setParameter(name: string, value: any): void;
  getParameter(name: string): any;
  setCurrentState(stateName: string): void;
  update(deltaTime: number): void;
  getCurrentState(): AnimationStateData | null;
  getPreviousState(): AnimationStateData | null;
  getStates(): AnimationStateData[];
  getTransitions(): TransitionData[];
  getParameters(): Map<string, any>;
  reset(): void;
  clear(): void;
}

// 动画状态类
export declare class AnimationState {
  readonly name: string;
  readonly type: string;

  constructor(name: string, type: string);
  getName(): string;
  getType(): string;
}

// 系统类声明
export declare class AnimationSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class AudioSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class PhysicsSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class RenderSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class InputSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class NetworkSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class UISystem extends System {
  static readonly type: string;
  constructor();
}

// 布局相关类
export declare class AbsoluteLayout extends Component {
  static readonly type: string;
  constructor();
}

export declare class FlexLayout extends Component {
  static readonly type: string;
  constructor();
}

export declare class GridLayout extends Component {
  static readonly type: string;
  constructor();
}

export declare class RelativeLayout extends Component {
  static readonly type: string;
  constructor();
}

// 动作控制相关
export enum ActionPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ActionType {
  INSTANT = 'instant',
  CONTINUOUS = 'continuous',
  TOGGLE = 'toggle'
}

export declare class ActionControlSystem extends System {
  static readonly type: string;
  constructor();
}

// 高级角色控制器
export declare class AdvancedCharacterController extends Component {
  static readonly type: string;
  constructor();
}

// 资产管理相关
export declare class AssetLoader extends EventEmitter {
  constructor();
  loadAsset(url: string, type: AssetType): Promise<any>;
  unloadAsset(url: string): void;
}

export declare class AssetManager extends EventEmitter {
  static getInstance(): AssetManager;
  constructor();
  registerLoader(type: AssetType, loader: AssetLoader): void;
  loadAsset(url: string, type?: AssetType): Promise<any>;
  getAsset(url: string): any | null;
  unloadAsset(url: string): void;
}

// 广告牌模式
export enum BillboardMode {
  NONE = 'none',
  CAMERA = 'camera',
  AXIS_Y = 'axisY',
  AXIS_XYZ = 'axisXYZ'
}

// 混合空间
export declare class BlendSpace1D extends Component {
  static readonly type: string;
  constructor();
  addBlendPoint(value: number, clip: THREE.AnimationClip): void;
  removeBlendPoint(index: number): void;
  getBlendedClip(value: number): THREE.AnimationClip | null;
}

export declare class BlendSpace2D extends Component {
  static readonly type: string;
  constructor();
  addBlendPoint(x: number, y: number, clip: THREE.AnimationClip): void;
  removeBlendPoint(index: number): void;
  getBlendedClip(x: number, y: number): THREE.AnimationClip | null;
}

// 角色控制器相关
export declare class CharacterController extends Component {
  static readonly type: string;
  constructor();
  move(direction: THREE.Vector3): void;
  jump(): void;
  setMovementMode(mode: CharacterMovementMode): void;
  getMovementMode(): CharacterMovementMode;
}

export declare class CharacterControllerComponent extends Component {
  static readonly type: string;
  constructor();
}

export declare class CharacterControllerPresetManager {
  static getInstance(): CharacterControllerPresetManager;
  getPreset(type: ControllerPresetType): any;
  setPreset(type: ControllerPresetType, preset: any): void;
}

// 自定义事件
export declare class CustomEvent extends EventEmitter {
  readonly type: string;
  readonly data: any;

  constructor(type: string, data?: any);
  getType(): string;
  getData(): any;
}

// 情感混合控制器
export declare class EmotionBlendController extends Component {
  static readonly type: string;
  constructor();
  setEmotion(emotion: string, intensity: number): void;
  getEmotion(emotion: string): number;
  blendEmotions(): void;
}

// 增强物理调试器
export declare class EnhancedPhysicsDebugger extends Component {
  static readonly type: string;
  constructor();
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
}

// 执行上下文
export declare class ExecutionContext {
  constructor();
  execute(fn: Function): any;
  dispose(): void;
}

// 纤程
export declare class Fiber {
  constructor(fn: Function);
  run(): void;
  pause(): void;
  resume(): void;
  dispose(): void;
}

// 约束相关
export declare class FixedConstraint extends Component {
  static readonly type: string;
  constructor();
}

export declare class SliderConstraint extends Component {
  static readonly type: string;
  constructor();
}

export declare class WheelConstraint extends Component {
  static readonly type: string;
  constructor();
}

// GLTF相关
export declare class GLTFAnimationComponent extends Component {
  static readonly type: string;
  constructor();
  loadAnimations(gltf: any): void;
  playAnimation(name: string): void;
  stopAnimation(name: string): void;
}

export declare class GLTFExporter extends EventEmitter {
  constructor();
  exportScene(scene: Scene, options?: any): Promise<ArrayBuffer | object>;
  exportEntity(entity: Entity, options?: any): Promise<ArrayBuffer | object>;
}

export declare class GLTFLoader extends EventEmitter {
  constructor();
  loadGLTF(url: string): Promise<any>;
  parseGLTF(data: ArrayBuffer): Promise<any>;
}

export declare class GLTFModelComponent extends Component {
  static readonly type: string;
  constructor();
  loadModel(url: string): Promise<void>;
  getModel(): THREE.Object3D | null;
}

export declare class GLTFNodeComponent extends Component {
  static readonly type: string;
  constructor();
}

export declare class GLTFSystem extends System {
  static readonly type: string;
  constructor();
}

// 输入设备相关
export declare class BaseInputDevice extends EventEmitter {
  readonly type: string;
  enabled: boolean;

  constructor(type: string);
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  update(): void;
  dispose(): void;
}

export declare class GamepadDevice extends BaseInputDevice {
  static readonly type: string;
  readonly gamepad: Gamepad;

  constructor(gamepad: Gamepad);
  getButton(index: number): boolean;
  getAxis(index: number): number;
}

export declare class KeyboardDevice extends BaseInputDevice {
  static readonly type: string;

  constructor();
  isKeyDown(key: string): boolean;
  isKeyPressed(key: string): boolean;
  isKeyReleased(key: string): boolean;
}

export declare class MouseDevice extends BaseInputDevice {
  static readonly type: string;

  constructor();
  getPosition(): { x: number; y: number };
  getDelta(): { x: number; y: number };
  isButtonDown(button: MouseButton): boolean;
  isButtonPressed(button: MouseButton): boolean;
  isButtonReleased(button: MouseButton): boolean;
}

export declare class TouchDevice extends BaseInputDevice {
  static readonly type: string;

  constructor();
  getTouches(): Touch[];
  getTouchCount(): number;
}

// 输入动作和映射
export declare class BaseInputAction extends EventEmitter {
  readonly type: InputActionType;
  enabled: boolean;

  constructor(type: InputActionType);
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  getValue(): any;
  update(): void;
}

export declare class ButtonInputAction extends BaseInputAction {
  constructor();
  isPressed(): boolean;
  isDown(): boolean;
  isReleased(): boolean;
}

export declare class ValueInputAction extends BaseInputAction {
  constructor();
  getValue(): number;
}

export declare class VectorInputAction extends BaseInputAction {
  constructor();
  getValue(): THREE.Vector2 | THREE.Vector3;
}

export declare class BaseInputMapping {
  readonly type: InputMappingType;

  constructor(type: InputMappingType);
  getType(): InputMappingType;
  evaluate(): any;
}

export declare class ButtonInputMapping extends BaseInputMapping {
  constructor();
  evaluate(): boolean;
}

export declare class AxisInputMapping extends BaseInputMapping {
  constructor();
  evaluate(): number;
}

export declare class VectorInputMapping extends BaseInputMapping {
  constructor();
  evaluate(): THREE.Vector2 | THREE.Vector3;
}

export declare class CompositeInputBinding {
  readonly type: CompositeBindingType;

  constructor(type: CompositeBindingType);
  addBinding(binding: any): void;
  removeBinding(binding: any): void;
  evaluate(): any;
}

export declare class InputBinding {
  constructor();
  bind(device: BaseInputDevice, mapping: BaseInputMapping): void;
  unbind(): void;
  evaluate(): any;
}

export declare class InputComponent extends Component {
  static readonly type: string;
  constructor();
  addAction(name: string, action: BaseInputAction): void;
  removeAction(name: string): void;
  getAction(name: string): BaseInputAction | null;
}

export declare class InputManager extends EventEmitter {
  static getInstance(): InputManager;

  constructor();
  addDevice(device: BaseInputDevice): void;
  removeDevice(device: BaseInputDevice): void;
  getDevice(type: string): BaseInputDevice | null;
  getDevices(): BaseInputDevice[];
  update(): void;
}

export declare class InputRecorder extends EventEmitter {
  constructor();
  startRecording(): void;
  stopRecording(): void;
  playback(): void;
  getRecording(): any[];
}

export declare class InputVisualizer extends Component {
  static readonly type: string;
  constructor();
  setVisible(visible: boolean): void;
  isVisible(): boolean;
}

// 交互系统相关
export declare class InteractableComponent extends Component {
  static readonly type: string;
  constructor();
  setInteractionType(type: InteractionType): void;
  getInteractionType(): InteractionType;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
}

export declare class InteractionEvent extends EventEmitter {
  readonly type: InteractionEventType;
  readonly target: Entity;
  readonly data: any;

  constructor(type: InteractionEventType, target: Entity, data?: any);
  getType(): InteractionEventType;
  getTarget(): Entity;
  getData(): any;
}

export declare class InteractionEventComponent extends Component {
  static readonly type: string;
  constructor();
  addEventListener(type: InteractionEventType, callback: (event: InteractionEvent) => void): void;
  removeEventListener(type: InteractionEventType, callback: (event: InteractionEvent) => void): void;
}

export declare class InteractionHighlightComponent extends Component {
  static readonly type: string;
  constructor();
  setHighlightType(type: HighlightType): void;
  getHighlightType(): HighlightType;
  setHighlighted(highlighted: boolean): void;
  isHighlighted(): boolean;
}

export declare class InteractionPromptComponent extends Component {
  static readonly type: string;
  constructor();
  setPrompt(text: string): void;
  getPrompt(): string;
  setPosition(position: PromptPositionType): void;
  getPosition(): PromptPositionType;
}

export declare class InteractionSystem extends System {
  static readonly type: string;
  constructor();
}

// 抓取系统相关
export declare class GrabbableComponent extends Component {
  static readonly type: string;
  constructor();
  setGrabType(type: GrabType): void;
  getGrabType(): GrabType;
  setGrabbable(grabbable: boolean): void;
  isGrabbable(): boolean;
}

export declare class GrabbedComponent extends Component {
  static readonly type: string;
  constructor();
  getGrabber(): Entity | null;
  setGrabber(grabber: Entity | null): void;
}

export declare class GrabberComponent extends Component {
  static readonly type: string;
  constructor();
  getGrabbedEntity(): Entity | null;
  setGrabbedEntity(entity: Entity | null): void;
  getGrabState(): GrabState;
}

export declare class GrabSystem extends System {
  static readonly type: string;
  constructor();
}

// 手部相关
export declare class Hand extends Component {
  static readonly type: string;
  constructor();
  getFingers(): any[];
  getPalm(): any;
  getWrist(): any;
}

// 物理系统相关
export declare class PhysicsBodyComponent extends Component {
  static readonly type: string;
  constructor();
  setMass(mass: number): void;
  getMass(): number;
  setVelocity(velocity: THREE.Vector3): void;
  getVelocity(): THREE.Vector3;
  applyForce(force: THREE.Vector3, point?: THREE.Vector3): void;
  applyImpulse(impulse: THREE.Vector3, point?: THREE.Vector3): void;
}

export declare class PhysicsCollider {
  constructor();
  setShape(shape: any): void;
  getShape(): any;
  setMaterial(material: any): void;
  getMaterial(): any;
}

export declare class PhysicsColliderComponent extends Component {
  static readonly type: string;
  readonly collider: PhysicsCollider;

  constructor();
  getCollider(): PhysicsCollider;
  setTrigger(isTrigger: boolean): void;
  isTrigger(): boolean;
}

export declare class PhysicsConstraintComponent extends Component {
  static readonly type: string;
  constructor();
  setConstraintType(type: ConstraintType): void;
  getConstraintType(): ConstraintType;
  setConnectedBody(body: PhysicsBodyComponent): void;
  getConnectedBody(): PhysicsBodyComponent | null;
}

export declare class PhysicsDebugger extends Component {
  static readonly type: string;
  constructor();
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  setWireframe(wireframe: boolean): void;
  isWireframe(): boolean;
}

export declare class PhysicsGrabComponent extends Component {
  static readonly type: string;
  constructor();
}

export declare class PhysicsRaycastResult {
  readonly hit: boolean;
  readonly point: THREE.Vector3;
  readonly normal: THREE.Vector3;
  readonly distance: number;
  readonly entity: Entity | null;

  constructor(hit: boolean, point?: THREE.Vector3, normal?: THREE.Vector3, distance?: number, entity?: Entity);
}

export declare class PhysicsWorldComponent extends Component {
  static readonly type: string;
  constructor();
  raycast(origin: THREE.Vector3, direction: THREE.Vector3, maxDistance?: number): PhysicsRaycastResult;
  sphereCast(origin: THREE.Vector3, radius: number, direction: THREE.Vector3, maxDistance?: number): PhysicsRaycastResult[];
  setGravity(gravity: THREE.Vector3): void;
  getGravity(): THREE.Vector3;
}

// 软体物理相关
export declare class SoftBodyComponent extends Component {
  static readonly type: string;
  constructor();
  setSoftBodyType(type: SoftBodyType): void;
  getSoftBodyType(): SoftBodyType;
  setStiffness(stiffness: number): void;
  getStiffness(): number;
}

export declare class SoftBodySystem extends System {
  static readonly type: string;
  constructor();
}

// 粒子系统相关
export declare class Particle {
  position: THREE.Vector3;
  velocity: THREE.Vector3;
  acceleration: THREE.Vector3;
  life: number;
  maxLife: number;
  size: number;
  color: THREE.Color;

  constructor();
  update(deltaTime: number): void;
  isAlive(): boolean;
}

export declare class ParticleEmitter extends Component {
  static readonly type: string;
  constructor();
  setEmissionRate(rate: number): void;
  getEmissionRate(): number;
  setMaxParticles(max: number): void;
  getMaxParticles(): number;
  setShapeType(type: EmitterShapeType): void;
  getShapeType(): EmitterShapeType;
  emitParticles(): void;
  stop(): void;
}

export declare class ParticleSystem extends System {
  static readonly type: string;
  constructor();
}

// 网络系统相关
export declare class NetworkConnection extends EventEmitter {
  readonly state: NetworkConnectionState;

  constructor();
  connect(url: string): Promise<void>;
  disconnect(): void;
  send(message: any): void;
  getState(): NetworkConnectionState;
}

export declare class NetworkEntityComponent extends Component {
  static readonly type: string;
  readonly entityType: NetworkEntityType;
  readonly syncMode: NetworkEntitySyncMode;
  readonly ownershipMode: NetworkEntityOwnershipMode;

  constructor();
  setEntityType(type: NetworkEntityType): void;
  getEntityType(): NetworkEntityType;
  setSyncMode(mode: NetworkEntitySyncMode): void;
  getSyncMode(): NetworkEntitySyncMode;
  setOwnershipMode(mode: NetworkEntityOwnershipMode): void;
  getOwnershipMode(): NetworkEntityOwnershipMode;
}

export declare class NetworkTransformComponent extends Component {
  static readonly type: string;
  constructor();
  syncTransform(): void;
  setInterpolation(enabled: boolean): void;
  isInterpolationEnabled(): boolean;
}

export declare class NetworkUserComponent extends Component {
  static readonly type: string;
  readonly role: NetworkUserRole;
  readonly state: NetworkUserState;

  constructor();
  setRole(role: NetworkUserRole): void;
  getRole(): NetworkUserRole;
  setState(state: NetworkUserState): void;
  getState(): NetworkUserState;
}

export declare class NetworkManager extends EventEmitter {
  static getInstance(): NetworkManager;

  constructor();
  createConnection(type: string): NetworkConnection;
  getConnection(id: string): NetworkConnection | null;
  removeConnection(id: string): void;
  broadcast(message: any): void;
}

export declare class WebRTCConnection extends NetworkConnection {
  readonly connectionState: WebRTCConnectionState;

  constructor();
  createOffer(): Promise<RTCSessionDescriptionInit>;
  createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit>;
  setRemoteDescription(description: RTCSessionDescriptionInit): Promise<void>;
  addIceCandidate(candidate: RTCIceCandidateInit): Promise<void>;
}

export declare class WebSocketConnection extends NetworkConnection {
  readonly connectionState: WebSocketConnectionState;

  constructor();
  connect(url: string): Promise<void>;
  send(data: string | ArrayBuffer): void;
}

export declare class MessageSerializer {
  static serialize(message: any): ArrayBuffer;
  static deserialize(data: ArrayBuffer): any;
  static compress(data: ArrayBuffer, level: CompressionLevel): ArrayBuffer;
  static decompress(data: ArrayBuffer): ArrayBuffer;
}

// 材质系统相关
export declare class MaterialConverter {
  static convertToStandard(material: THREE.Material): THREE.MeshStandardMaterial;
  static convertToPhysical(material: THREE.Material): THREE.MeshPhysicalMaterial;
  static optimizeMaterial(material: THREE.Material): THREE.Material;
}

export declare class MaterialFactory {
  static createMaterial(type: MaterialType, options?: any): THREE.Material;
  static createStandardMaterial(options?: any): THREE.MeshStandardMaterial;
  static createPhysicalMaterial(options?: any): THREE.MeshPhysicalMaterial;
}

export declare class MaterialOptimizer {
  static optimizeMaterials(materials: THREE.Material[]): THREE.Material[];
  static mergeMaterials(materials: THREE.Material[]): THREE.Material;
  static reduceTextureSize(material: THREE.Material, factor: number): void;
}

export declare class MaterialSystem extends System {
  static readonly type: string;
  constructor();
  registerMaterial(name: string, material: THREE.Material): void;
  getMaterial(name: string): THREE.Material | null;
  optimizeAllMaterials(): void;
}

// UI系统相关
export declare class UI2DComponent extends Component {
  static readonly type: string;
  constructor();
  setPosition(x: number, y: number): void;
  getPosition(): { x: number; y: number };
  setSize(width: number, height: number): void;
  getSize(): { width: number; height: number };
}

export declare class UI2DSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class UI3DComponent extends Component {
  static readonly type: string;
  constructor();
  setWorldPosition(position: THREE.Vector3): void;
  getWorldPosition(): THREE.Vector3;
  setScale(scale: number): void;
  getScale(): number;
}

export declare class UI3DSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class UIAnimation {
  readonly type: UIAnimationType;
  readonly duration: number;
  readonly easing: UIEasing;

  constructor(type: UIAnimationType, duration: number, easing?: UIEasing);
  play(): void;
  pause(): void;
  stop(): void;
  setProgress(progress: number): void;
  getProgress(): number;
}

export declare class UIAnimationComponent extends Component {
  static readonly type: string;
  constructor();
  addAnimation(name: string, animation: UIAnimation): void;
  removeAnimation(name: string): void;
  playAnimation(name: string): void;
  stopAnimation(name: string): void;
}

export declare class UIAnimationSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class UIComponent extends Component {
  static readonly type: string;
  readonly componentType: UIComponentType;

  constructor(type: UIComponentType);
  getComponentType(): UIComponentType;
  setVisible(visible: boolean): void;
  isVisible(): boolean;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
}

export declare class UIEasing {
  static linear(t: number): number;
  static easeIn(t: number): number;
  static easeOut(t: number): number;
  static easeInOut(t: number): number;
}

export declare class UIEvent extends EventEmitter {
  readonly type: UIEventType;
  readonly target: UIComponent;
  readonly data: any;

  constructor(type: UIEventType, target: UIComponent, data?: any);
  getType(): UIEventType;
  getTarget(): UIComponent;
  getData(): any;
}

export declare class UIEventComponent extends Component {
  static readonly type: string;
  constructor();
  addEventListener(type: UIEventType, callback: (event: UIEvent) => void): void;
  removeEventListener(type: UIEventType, callback: (event: UIEvent) => void): void;
}

export declare class UILayoutComponent extends Component {
  static readonly type: string;
  readonly layoutType: UILayoutType;

  constructor(type: UILayoutType);
  getLayoutType(): UILayoutType;
  setLayoutType(type: UILayoutType): void;
  updateLayout(): void;
}

export declare class UILayoutSystem extends System {
  static readonly type: string;
  constructor();
}

// 可视化脚本系统相关
export declare class VisualScriptComponent extends Component {
  static readonly type: string;
  constructor();
  loadScript(url: string): Promise<void>;
  executeScript(): void;
  setVariable(name: string, value: any): void;
  getVariable(name: string): any;
}

export declare class VisualScriptEngine extends EventEmitter {
  static getInstance(): VisualScriptEngine;

  constructor();
  createGraph(): Graph;
  loadGraph(data: any): Graph;
  saveGraph(graph: Graph): any;
  executeGraph(graph: Graph): void;
}

export declare class VisualScriptSystem extends System {
  static readonly type: string;
  constructor();
}

export declare class Graph extends EventEmitter {
  readonly nodes: Map<string, Node>;
  readonly connections: any[];

  constructor();
  addNode(node: Node): void;
  removeNode(node: Node): void;
  getNode(id: string): Node | null;
  connectNodes(outputNode: Node, inputNode: Node): void;
  disconnectNodes(outputNode: Node, inputNode: Node): void;
  execute(): void;
}

export declare class Node extends EventEmitter {
  readonly id: string;
  readonly type: NodeType;
  readonly category: NodeCategory;

  constructor(type: NodeType, category: NodeCategory);
  getId(): string;
  getType(): NodeType;
  getCategory(): NodeCategory;
  execute(): void;
}

export declare class Variable {
  readonly name: string;
  readonly type: string;
  value: any;

  constructor(name: string, type: string, value?: any);
  getName(): string;
  getType(): string;
  getValue(): any;
  setValue(value: any): void;
}

export declare class ValueTypeRegistry {
  static registerType(name: string, type: any): void;
  static getType(name: string): any;
  static hasType(name: string): boolean;
  static getAllTypes(): Map<string, any>;
}

export declare class NodeRegistry {
  static registerNode(type: NodeType, nodeClass: any): void;
  static getNode(type: NodeType): any;
  static hasNode(type: NodeType): boolean;
  static getAllNodes(): Map<NodeType, any>;
}

// 枚举定义
export enum NodeCategory {
  CORE = 'core',
  MATH = 'math',
  LOGIC = 'logic',
  INPUT = 'input',
  OUTPUT = 'output',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  PHYSICS = 'physics',
  NETWORK = 'network',
  ENTITY = 'entity',
  CUSTOM = 'custom'
}

export enum NodeType {
  FUNCTION = 'function',
  EVENT = 'event',
  FLOW = 'flow',
  ASYNC = 'async'
}

export enum SocketDirection {
  INPUT = 'input',
  OUTPUT = 'output'
}

export enum SocketType {
  EXEC = 'exec',
  DATA = 'data'
}

export enum AsyncNodeState {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 国际化相关
export declare class I18n extends EventEmitter {
  static getInstance(): I18n;

  constructor();
  setLanguage(language: string): void;
  getLanguage(): string;
  translate(key: string, params?: any): string;
  addTranslations(language: string, translations: any): void;
  hasTranslation(key: string, language?: string): boolean;
}

// 天空盒相关
export declare class Skybox extends Component {
  static readonly type: string;
  readonly skyboxType: SkyboxType;

  constructor(type?: SkyboxType);
  setSkyboxType(type: SkyboxType): void;
  getSkyboxType(): SkyboxType;
  setTexture(texture: THREE.Texture): void;
  getTexture(): THREE.Texture | null;
  setColor(color: THREE.Color): void;
  getColor(): THREE.Color;
}

// XR设备相关
export declare class XRDevice extends EventEmitter {
  readonly type: string;
  readonly connected: boolean;

  constructor(type: string);
  connect(): Promise<void>;
  disconnect(): void;
  isConnected(): boolean;
  getControllers(): any[];
  getHeadset(): any;
}

// 节点注册函数
export declare function registerAnimationNodes(): void;
export declare function registerAudioNodes(): void;
export declare function registerCoreNodes(): void;
export declare function registerEntityNodes(): void;
export declare function registerInputNodes(): void;
export declare function registerMathNodes(): void;
export declare function registerNetworkNodes(): void;
export declare function registerPhysicsNodes(): void;

// 工具函数
export declare function generateUUID(): string;
export declare function generateShortId(): string;
export declare function isValidUUID(uuid: string): boolean;
export declare function registerComponent<T extends Component>(componentClass: new (...args: any[]) => T): void;
export declare function getComponentClass<T extends Component>(type: string): (new (...args: any[]) => T) | null;
export declare function setEntityManager(manager: any): void;

// 导出默认引擎实例
declare const engine: Engine;
export default engine;
